import { Prisma, PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function main() {
  // Seed data for PropertyType
  const residentialCategory = await prisma.propertyCategories.upsert({
    where: { name: "Residential" },
    update: {},
    create: { name: "Residential" },
  });

  const commercialCategory = await prisma.propertyCategories.upsert({
    where: { name: "Commercial" },
    update: {},
    create: { name: "Commercial" },
  });

  const farmlandCategory = await prisma.propertyCategories.upsert({
    where: { name: "Farmland" },
    update: {},
    create: { name: "Farmland" },
  });

  const propertyTypes: Prisma.PropertyTypeCreateManyInput[] = [];
  ["Apartment", "Independent House", "House", "Land"].forEach((type) => {
    propertyTypes.push({
      name: type,
      categoryId: residentialCategory.id,
    });
  });

  ["Commercial", "Industrial", "Manufacturing"].forEach((type) => {
    propertyTypes.push({
      name: type,
      categoryId: commercialCategory.id,
    });
  });

  await prisma.propertyType.createMany({
    data: propertyTypes,
  });

  console.log("Property types seeded successfully.");
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
