// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
    provider        = "prisma-client-js"
    // output = "../generated/prisma"
    previewFeatures = ["fullTextSearchPostgres"]
    // binaryTargets = ["rhel-openssl-3.0.x","native"]
    //  output = "../../node_modules/.prisma/client"
}

generator drizzle {
  provider = "drizzle-prisma-generator"
  output   = "./drizzle" // Where to put generated Drizle tables
}
datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}

enum AdminUserTypeEnum {
    ADMIN
    MANAGER
}

model AdminUser {
    id           String            @id @default(cuid())
    name         String
    email        String            @unique
    passwordHash String
    userType     AdminUserTypeEnum @default(ADMIN)
    active       Boolean           @default(false)
    createdAt    DateTime          @default(now())
    updatedAt    DateTime          @updatedAt
    deletedAt    DateTime?

    // Users created by this admin (for role-based access control)
    createdUsers User[]

    // Audit logs performed by this admin
    auditLogs    AuditLog[]
}

enum ONBOARDING_STEP_ENUM {
    STEP_1
    STEP_2
    STEP_3
}

enum AuditActionEnum {
    CREATE
    UPDATE
    DELETE
    STATUS_CHANGE
    APPROVE
    REJECT
    ACTIVATE
    DEACTIVATE
    RESTORE
}

enum AuditEntityEnum {
    USER
    PROPERTY
    POST
    PARTNER
    COMPANY
    ADMIN_USER
}

model AuditLog {
    id          String           @id @default(cuid())
    action      AuditActionEnum
    entityType  AuditEntityEnum
    entityId    String
    entityTitle String?          // Human readable title/name of the entity

    // Who performed the action
    performedById   String
    performedBy     AdminUser    @relation(fields: [performedById], references: [id])
    performedByName String       // Cached name for historical records

    // What changed
    oldValues   Json?            // Previous state of the entity
    newValues   Json?            // New state of the entity
    changes     Json?            // Specific fields that changed

    // Additional context
    reason      String?          // Reason for the action (e.g., rejection reason)
    metadata    Json?            // Additional metadata about the action
    ipAddress   String?          // IP address of the user
    userAgent   String?          // User agent string

    createdAt   DateTime         @default(now())

    @@index([entityType, entityId])
    @@index([performedById])
    @@index([action])
    @@index([createdAt])
}

model User {
    id                  String    @id @default(cuid())
    name                String    @default("")
    email               String    @unique
    emailVerified       DateTime?
    passwordHash        String?
    accountType         String    @default("normal") // 'normal' | 'facebook' | 'google' | 'apple'
    uid                 String?
    token               String?
    refreshToken        String?
    resetPasswordToken  String?
    resetPasswordSentAt DateTime?
    active              Boolean   @default(false)
    statusUpdatedAt     DateTime?
    statusUpdateRemarks String?
    statusUpdatedBy     String?
    createdAt           DateTime  @default(now())
    updatedAt           DateTime  @updatedAt
    Account             Account[]
    Session             Session[]
    deletedAt           DateTime?

    phoneNumber      String           @unique @default("")
    pancardNumber    String?
    adharcardNumber  String?          @unique
    reraNumber       String?
    gstNumber        String?
    OtpSentAt        DateTime?
    verifiedAgent    Boolean?
    experience       String?
    propertiesSold   Int?
    rating           String?
    reviews          Int?
    bio              String?
    preference       PropertyForEnum?
    userLocation     String?
    latitude         String?
    longitude        String?
    fileKey          String?
    filePublicUrl    String?
    bgFileKey        String?
    bgFilePublicUrl  String?

    cloudinaryProfileImagePublicId String?
    cloudinaryProfileImageUrl      String?
    cloudinaryBgImagePublicId      String?
    cloudinaryBgImageUrl           String?

    inviteCode       String?          @unique @default(cuid())
    referredBy       String?
    referredByUserId String?

    isOnline   Boolean   @default(false)
    lastActive DateTime?
    totalViews Int       @default(0)

    lastNotificationCheckedAt DateTime?

    operationArea              OperationArea[]
    testimonials               Testimonials[]
    properties                 Property[]
    comments                   Comments[]
    sentConnectionRequests     ConnectionRequests[]       @relation("sender")
    receivedConnectionRequests ConnectionRequests[]       @relation("receiver")
    block                      ConnectionRequests[]       @relation("block")
    coustomerConnections       CustomerAgentConnections[]

    companyDetails CompanyDetails? @relation("adminUser")
    companyId      String?
    company        CompanyDetails? @relation(fields: [companyId], references: [id], name: "user", onDelete: Cascade)
    Favourites     Favourites[]
    likedAgents    LikedAgents[]

    recentlyViewed          RecentlyViewed[]
    //consists list of users who has viewed users profile
    viewedByUsers           ProfileView[]        @relation("viewedByUser")
    //consists list of profiles that user has viewed
    viewedToUsers           ProfileView[]        @relation("viewedToUser")
    ratingFromConversations Rating[]             @relation(name: "ratedBy")
    ratedToConversations    Rating[]             @relation(name: "ratedTo")
    notifications           Notification[]
    posts                   Post[]
    likes                   Like[]
    postComments            Comment[]
    cityId                  String?
    city                    City?                @relation(fields: [cityId], references: [id])
    onboardingStatus        Boolean              @default(false)
    onboardingStep          ONBOARDING_STEP_ENUM @default(STEP_1)
    onboardingPreference    String?

    customerRatingsToAgents CustomerRatingsToAgents[]
    reportedPost            ReportedPost[]

    walletBalanceInCents    Int                     @default(0)
    walletTransactions      WalletTransaction[]
    languages               Languages[]
    LikedAgentsByCustomer   LikedAgentsByCustomer[]
    reportedToUsersFromChat ReportedUserFromChat[]  @relation(name: "reportedUser")
    reportedByUsersFromChat ReportedUserFromChat[]  @relation(name: "reportingUser")

    // Role-based access control - tracks which admin created this user
    createdByAdminId        String?
    createdByAdmin          AdminUser?              @relation(fields: [createdByAdminId], references: [id])
}

model ProfileView {
    id             String @id @default(cuid())
    viewedByUserId String
    viewedToUserId String

    viewedByUser User     @relation(name: "viewedByUser", fields: [viewedByUserId], references: [id], onDelete: Cascade)
    viewedToUser User     @relation(name: "viewedToUser", fields: [viewedToUserId], references: [id], onDelete: Cascade)
    createdAt    DateTime @default(now())
    updatedAt    DateTime @updatedAt
}

model Languages {
    id                   String    @id @default(cuid())
    name                 String
    user                 User[]
    createdAt            DateTime
    updatedAt            DateTime  @updatedAt
    deletedAt            DateTime?
    walletBalanceInCents Int       @default(0)
}

model WalletTransaction {
    id            String   @id @default(cuid())
    message       String
    title         String   @default("Referral Points")
    amountInCents Int
    user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)
    userId        String
    createdAt     DateTime @default(now())
    updatedAt     DateTime @updatedAt
}

model Customer {
    id                    String    @id @default(cuid())
    name                  String    @default("")
    email                 String    @unique
    phoneNumber           String    @unique @default("")
    adharcardNumber       String?   @unique
    bio                   String?
    profileImageKey       String?
    profileImagePublicUrl String?

    cloudinaryImagePublicId String?
    cloudinaryImagePublicUrl  String?

    active                Boolean   @default(true)
    emailVerified         DateTime?
    onboardingStatus      Boolean   @default(false)
    onboardingPreference  String?
    locationAddress       String?
    latitude              String?
    longitude             String?

    cityId String?
    city   City?   @relation(fields: [cityId], references: [id])

    customerPropertyEnquiryForm CustomerPropertyEnquiryForm[]

    favouriteProperties CustomerFavourites[]

    connections CustomerAgentConnections[]

    inviteCode String?   @unique @default(cuid())
    createdAt  DateTime  @default(now())
    updatedAt  DateTime  @updatedAt
    deletedAt  DateTime?

    customerRatingsToAgents        CustomerRatingsToAgents[]
    customerPropertyViewingHistory CustomerPropertyViewingHistory[]
    LikedAgentsByCustomer          LikedAgentsByCustomer[]
    notifications                  Notification[]
    likes                          Like[]
    postComments                   Comment[]
    reportedPost                   ReportedPost[]
    ReportedUserFromChat           ReportedUserFromChat[]
}

model OperationArea {
    id                    String @id @default(cuid())
    name                  String
    areaLat               String
    areaLng               String
    areaGooglePlaceId     String
    areaAddressComponents Json

    userId String
    user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model CompanyDetails {
    id                 String  @id @default(cuid())
    companyName        String
    companyLocation    String
    companyLatitude    String
    companyLongitude   String
    email              String
    phoneNumber        String
    fax                String?
    about              String
    companyWebsiteLink String?

    cloudinaryCompanyLogoPublicId String?
    cloudinaryCompanyLogoUrl      String?

    fileKey            String?
    filePublicUrl      String?
    adminUser          User    @relation(fields: [adminUserId], references: [id], name: "adminUser", onDelete: Cascade)
    adminUserId        String  @unique
    companyAgents      User[]  @relation("user")
}

// Necessary for Next auth
model Account {
    id                String  @id @default(cuid())
    type              String
    provider          String
    providerAccountId String
    token_type        String?
    refresh_token     String?
    access_token      String?
    id_token          String?
    scope             String?
    session_state     String?
    expires_at        Int?

    user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
    userId String

    @@unique([provider, providerAccountId])
}

model Session {
    id           String   @id @default(cuid())
    sessionToken String   @unique
    userId       String
    expires      DateTime
    user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Testimonials {
    id          String @id @default(cuid())
    title       String
    description String
    stars       Int
    writerId    String
    writer      User   @relation(fields: [writerId], references: [id], onDelete: Cascade)
}

model CustomerTestimonials {
    id            String   @id @default(cuid())
    name          String
    description   String
    fileKey       String?
    filePublicUrl String?
    rating        Int      @default(0)
    cityId        String
    city          City    @relation(fields: [cityId], references: [id])
    createdAt     DateTime
    updatedAt     DateTime @updatedAt
}

enum EnquiryType {
    AGENT
    CUSTOMER
}

model Enquiries {
    id          String      @id @default(cuid())
    name        String      @default("")
    email       String
    phoneNumber String      @unique
    enquiryType EnquiryType
    verified    Boolean     @default(false)
    agentCode   String?
    createdAt   DateTime    @default(now())
    updatedAt   DateTime    @default(now()) @updatedAt
}

model Amenities {
    id            String     @id @default(cuid())
    name          String
    fileKey       String?
    filePublicUrl String?
    createdAt     DateTime   @default(now())
    updatedAt     DateTime   @updatedAt
    properties    Property[]
}

model PropertyType {
    id                          String                        @id @default(cuid())
    categoryId                  String?
    category                    PropertyCategories?           @relation(fields: [categoryId], references: [id])
    name                        String
    createdAt                   DateTime                      @default(now())
    updatedAt                   DateTime                      @updatedAt
    properties                  Property[]
    customerPropertyEnquiryForm CustomerPropertyEnquiryForm[]
}

enum PropertyCategoryEnum {
    RESIDENTIAL
    COMMERCIAL
}

enum PropertyForEnum {
    SALE
    RENT
}

model PropertyCategories {
    id                          String                        @id @default(cuid())
    name                        String                        @unique
    createdAt                   DateTime                      @default(now())
    updatedAt                   DateTime                      @updatedAt
    properties                  Property[]
    PropertyTypes               PropertyType[]
    customerPropertyEnquiryForm CustomerPropertyEnquiryForm[]
    AreaUnit                    AreaUnit[]

    // Field visiblity status(can be configured while creating categories in admin panel)
    // step 1 fields
    showBedrooms         PostPropertyFormFieldStatusEnum @default(HIDE)
    showBathrooms        PostPropertyFormFieldStatusEnum @default(HIDE)
    showSecurityDeposit  PostPropertyFormFieldStatusEnum @default(HIDE)
    showAreaIn           PostPropertyFormFieldStatusEnum @default(HIDE)
    showArea             PostPropertyFormFieldStatusEnum @default(HIDE)
    showAboutProperty    PostPropertyFormFieldStatusEnum @default(HIDE)
    // step 2 fields
    showPropertyAddress  PostPropertyFormFieldStatusEnum @default(HIDE)
    showPropertyLocation PostPropertyFormFieldStatusEnum @default(HIDE)
    showUtilities        PostPropertyFormFieldStatusEnum @default(HIDE)
    // step 3 fields
    showSocietyName      PostPropertyFormFieldStatusEnum @default(HIDE)
    showBuildYear        PostPropertyFormFieldStatusEnum @default(HIDE)
    showPossessionState  PostPropertyFormFieldStatusEnum @default(HIDE)
    showAmenities        PostPropertyFormFieldStatusEnum @default(HIDE)
    showFurnishing       PostPropertyFormFieldStatusEnum @default(HIDE)
    showFacing           PostPropertyFormFieldStatusEnum @default(HIDE)
    showTotalFloors      PostPropertyFormFieldStatusEnum @default(HIDE)
    showFloorNumber      PostPropertyFormFieldStatusEnum @default(HIDE)
    showCarParking       PostPropertyFormFieldStatusEnum @default(HIDE)
    showPropertyState    PostPropertyFormFieldStatusEnum @default(HIDE)
}

model Utilities {
    id           String   @id @default(cuid())
    utility      String
    distanceInKm Decimal  @db.Decimal(9, 2)
    createdAt    DateTime @default(now())
    updatedAt    DateTime @updatedAt
    property     Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
    propertyId   String
}

enum FacingEnum {
    NORTH
    SOUTH
    EAST
    WEST
    NORTH_EAST
    NORTH_WEST
    SOUTH_EAST
    SOUTH_WEST
}

// enum AreaInEnum {
//     SQUAREFEET
//     SQUAREMETER
//     SQUAREYARD
// }

model AreaUnit {
    id                   String               @id @default(cuid())
    name                 String
    shortForm            String
    conversionMultiplyer Float
    category             PropertyCategories[]
    createdAt            DateTime             @default(now())
    updatedAt            DateTime             @updatedAt
    Property             Property[]
}

enum FurnishingEnum {
    RAW
    SEMIFURNISHED
    FULLYFURNISHED
}

enum PropertyStateEnum {
    NEW
    RESALE
    UPCOMING
}

enum PossessionStateEnum {
    READY_TO_MOVE
    UNDER_6_MONTHS
    UNDER_1_YEAR
    UNDER_3_YEARS
}

enum PropertyStatusEnum {
    PENDING
    REJECTED
    InREVIEW
    ACTIVE
    INACTIVE
    DELETED
    SOLD
}

enum PostPropertyFormFieldStatusEnum {
    SHOW
    HIDE
    OPTIONAL
}

model PropertyMediaSection {
    id         String          @id @default(cuid())
    title      String
    createdAt  DateTime        @default(now())
    updatedAt  DateTime        @updatedAt
    property   Property        @relation(fields: [propertyId], references: [id], onDelete: Cascade)
    propertyId String
    media      PropertyMedia[]
}

enum PropertyMediaTypeEnum {
    IMAGE
    VIDEO
}

model PropertyMedia {
    id                     String               @id @default(cuid())
    fileKey                String?
    filePublicUrl          String?
    mediaType              PropertyMediaTypeEnum?
    cloudinaryId           String?
    cloudinaryUrl          String?

    createdAt              DateTime             @default(now())
    updatedAt              DateTime             @updatedAt
    propertyMediaSection   PropertyMediaSection @relation(fields: [propertyMediaSectionId], references: [id], onDelete: Cascade)
    propertyMediaSectionId String
}

model Property {
    id                        String                 @id @default(cuid())
    // step 1
    registeryFileKey          String?
    propertyTitle             String?
    propertyFor               PropertyForEnum
    // propertyCategory          PropertyCategoryEnum
    bedrooms                  Int?
    bathrooms                 Int?
    propertyPrice             Decimal?               @db.Money
    securityDeposit           Decimal?               @db.Money
    // areaIn                    AreaInEnum
    areaUnitId                String?
    areaUnit                  AreaUnit?              @relation(fields: [areaUnitId], references: [id])
    area                      Float?
    areaInSqMeters            Float?
    aboutProperty             String?
    // step 2
    propertyAddress           String?
    propertyLatitude          Float?
    propertyLongitude         Float?
    propertyGooglePlaceId     String?
    propertyAddressComponents Json?
    propertyMarkersLatLng     Json?
    propertyLocation          String?
    utilities                 Utilities[]
    // Step 3
    societyOrLocalityName     String?
    buildYear                 Int?
    possessionState           PossessionStateEnum?
    amenities                 Amenities[]
    furnishing                FurnishingEnum?
    totalFloors               Int?
    floorNumber               Int?
    carParking                Int?
    facing                    FacingEnum?
    propertyState             PropertyStateEnum?
    // Step 4
    mediaSections             PropertyMediaSection[]
    comments                  Comments[]
    createdAt                 DateTime               @default(now())
    updatedAt                 DateTime               @updatedAt
    propertyType              PropertyType           @relation(fields: [propertyTypeId], references: [id])
    propertyTypeId            String
    propertyStatus            PropertyStatusEnum?    @default(PENDING)
    statusUpdatedAt           DateTime?
    statusUpdateRemarks       String?
    statusUpdatedBy           String?
    user                      User                   @relation(fields: [userId], references: [id], onDelete: Cascade)
    userId                    String
    rating                    String?
    review                    String?
    popularProperty           Boolean?
    connections               ConnectionRequests[]
    Favourites                Favourites[]
    customerFavourites        CustomerFavourites[]
    viewedByUsers             RecentlyViewed[]
    soldAt                    DateTime?
    ratingFromConversation    Rating[]
    totalViews                Int                    @default(0)
    PropertyCategory          PropertyCategories?    @relation(fields: [propertyCategoryId], references: [id])
    propertyCategoryId        String?

    customerAgentConnections       CustomerAgentConnections[]
    CustomerRatingsToAgents        CustomerRatingsToAgents[]
    customerPropertyViewingHistory CustomerPropertyViewingHistory[]
}

model Feedback {
    id                                      String   @id @default(cuid())
    agentName                               String
    companyName                             String
    rating                                  Int
    whatDoYouLikeAboutTheAgent              Json?
    doYouHaveAnythingElseToAddAboutTheAgent String
    createdAt                               DateTime @default(now())
    updatedAt                               DateTime @updatedAt
}

model PartnerResume {
    id          String   @id @default(cuid())
    name        String
    email       String   @unique
    phoneNumber String   @unique
    jobRoleId   String
    jobRole     JobRole  @relation(fields: [jobRoleId], references: [id])
    resumeKey   String
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt
}

model JobRole {
    id            String          @id @default(cuid())
    name          String
    createdAt     DateTime        @default(now())
    updatedAt     DateTime        @updatedAt
    partnerResume PartnerResume[]
    deletedAt     DateTime?
}

model DeleteAccount {
    id          String    @id @default(cuid())
    email       String
    phoneNumber String
    reason      String
    createdAt   DateTime  @default(now())
    deletedAt   DateTime?
}

model DeleteCustomerAccount {
    id          String    @id @default(cuid())
    email       String
    phoneNumber String
    reason      String
    createdAt   DateTime  @default(now())
    deletedAt   DateTime?
}

model Comments {
    id          String    @id @default(cuid())
    userId      String
    user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
    createdAt   DateTime  @default(now())
    deletedAt   DateTime?
    description String
    propertyId  String
    property    Property  @relation(fields: [propertyId], references: [id], onDelete: Cascade)
}

enum ProjectEnum {
    B2B_DEER_CONNECT
    B2C_MY_DEER
}

enum FaqPagesEnum {
    HELP_CENTER_PAGE
    DELETE_ACCOUNT_PAGE
    CAREER_PAGE
    CONTACT_US_PAGE
}

model Faq {
    id        String   @id @default(cuid())
    question  String
    answer    String
    order     Int
    project   ProjectEnum?
    page      FaqPagesEnum?
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

enum ConnectionRequestsEnum {
    ACCEPTED
    REJECTED
    PENDING
}

model ConnectionRequests {
    id                          String                 @id @default(cuid())
    senderId                    String
    receiverId                  String
    status                      ConnectionRequestsEnum @default(PENDING)
    senderUnseenMessagesCount   Int                    @default(0)
    receiverUnseenMessagesCount Int                    @default(0)

    deletedAt DateTime?

    blocked       Boolean @default(false)
    blockReason   String?
    blockByUserId String?
    blockByUser   User?   @relation(fields: [blockByUserId], references: [id], name: "block", onDelete: Cascade)

    propertyId String?
    property   Property? @relation(fields: [propertyId], references: [id], onDelete: Cascade)

    ratingId String?
    rating   Rating[]

    sender   User @relation(fields: [senderId], references: [id], name: "sender", onDelete: Cascade)
    receiver User @relation(fields: [receiverId], references: [id], name: "receiver", onDelete: Cascade)

    messages Message[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model Message {
    id      String  @id @default(cuid())
    content String
    read    Boolean

    senderId String

    connectionId String
    connection   ConnectionRequests @relation(fields: [connectionId], references: [id], onDelete: Cascade)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model Favourites {
    id         String   @id @default(cuid())
    userId     String
    propertyId String
    property   Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
    user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
    createdAt  DateTime @default(now())
    updatedAt  DateTime @updatedAt
}

model LikedAgents {
    id           String   @id @default(cuid())
    userId       String
    user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
    likedAgentId String
    createdAt    DateTime @default(now())
    updatedAt    DateTime @updatedAt
}

model LikedAgentsByCustomer {
    id           String   @id @default(cuid())
    customerId   String
    customer     Customer @relation(fields: [customerId], references: [id])
    likedAgentId String
    likedAgent   User     @relation(fields: [likedAgentId], references: [id], onDelete: Cascade)
    createdAt    DateTime @default(now())
    updatedAt    DateTime @updatedAt
}

model RecentlyViewed {
    id         String @id @default(cuid())
    userId     String
    propertyId String

    user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)
    property Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model News {
    id            String   @id @default(cuid())
    title         String
    description   String
    fileKey       String
    filePublicUrl String
    redirectUrl   String
    createdAt     DateTime @default(now())
    updatedAt     DateTime @updatedAt
}

model Rating {
    id                    String  @id @default(cuid())
    propertyStarsCount    Int?
    propertyRatingMessage String?
    userStarsCount        Int
    userRatingMessage     String

    propertyId    String?
    ratedByUserId String
    ratedToUserId String
    connectionId  String?

    connection ConnectionRequests? @relation(fields: [connectionId], references: [id], onDelete: Cascade)
    property   Property?           @relation(fields: [propertyId], references: [id], onDelete: Cascade)
    ratedTo    User                @relation(fields: [ratedToUserId], references: [id], name: "ratedTo", onDelete: Cascade)
    ratedBy    User                @relation(fields: [ratedByUserId], references: [id], name: "ratedBy", onDelete: Cascade)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

enum NotificationEnum {
    CHAT
    CONNECTION
    POST
    WALLET
    CUSTOMER_CHAT
    CUSTOMER_CONNECTION
    PROPERTY
}

model Notification {
    id          String           @id @default(cuid())
    title       String
    description String
    type        NotificationEnum
    metaData    Json

    customerId String?
    customer   Customer? @relation(fields: [customerId], references: [id])
    receiverId String?
    receiver   User?     @relation(fields: [receiverId], references: [id], onDelete: Cascade)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

enum PostsMediaTypeEnum {
    VIDEO
    IMAGE
}

model Post {
    id            String         @id @default(cuid())
    content       String
    totalLikes    Int
    totalComments Int
    reportCount   Int            @default(0)
    userId        String
    media         PostsMedia[]
    likes         Like[]
    comments      Comment[]
    user          User           @relation(fields: [userId], references: [id], onDelete: Cascade)
    deletedAt     DateTime?
    createdAt     DateTime       @default(now())
    updatedAt     DateTime       @updatedAt
    reportedPost  ReportedPost[]
}

model ReportedPost {
    id         String    @id @default(cuid())
    reason     String
    userId     String?
    customerId String?
    user       User?     @relation(fields: [userId], references: [id], onDelete: Cascade)
    customer   Customer? @relation(fields: [customerId], references: [id])
    postId     String
    post       Post      @relation(fields: [postId], references: [id], onDelete: Cascade)
    createdAt  DateTime? @default(now())
    updatedAt  DateTime? @updatedAt
}

model PostsMedia {
    id            String             @id @default(cuid())
    fileKey       String?
    filePublicUrl String?
    cloudinaryId  String?
    cloudinaryUrl String?
    mediaType     PostsMediaTypeEnum

    postId String
    post   Post   @relation(fields: [postId], references: [id], onDelete: Cascade)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model Like {
    id         String  @id @default(cuid())
    userId     String?
    customerId String?
    postId     String

    post     Post      @relation(fields: [postId], references: [id], onDelete: Cascade)
    user     User?     @relation(fields: [userId], references: [id], onDelete: Cascade)
    customer Customer? @relation(fields: [customerId], references: [id])

    createdAt DateTime @default(now())
}

model Comment {
    id       String  @id @default(cuid())
    comment  String
    isPinned Boolean

    userId     String?
    customerId String?
    postId     String
    user       User?     @relation(fields: [userId], references: [id], onDelete: Cascade)
    customer   Customer? @relation(fields: [customerId], references: [id])
    post       Post      @relation(fields: [postId], references: [id], onDelete: Cascade)

    createdAt DateTime  @default(now())
    deletedAt DateTime?
}

model City {
    id String @id @default(cuid())

    name              String
    cityMarkersLatLng Json
    northMaxLat       Float?
    southMaxLat       Float?
    eastMaxLng        Float?
    westMaxLng        Float?

    customers                  Customer[]
    users                      User[]
    customerPropertyUniqueForm CustomerPropertyEnquiryForm[]
    CustomerTestimonials       CustomerTestimonials[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model CustomerFavourites {
    id         String   @id @default(cuid())
    customerId String
    propertyId String
    property   Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
    user       Customer @relation(fields: [customerId], references: [id])
    createdAt  DateTime @default(now())
    updatedAt  DateTime @updatedAt
}

model CustomerPropertyEnquiryForm {
    id                  String              @id @default(cuid())
    propertyCategory    PropertyCategories? @relation(fields: [propertyCategoryId], references: [id])
    propertyCategoryId  String
    propertyType        PropertyType        @relation(fields: [propertyTypeId], references: [id])
    propertyTypeId      String
    city                City                @relation(fields: [cityId], references: [id])
    cityId              String
    propertyPrice       BigInt[]
    propertyPossession  PossessionStateEnum
    propertyRequirement String?
    customer            Customer            @relation(fields: [customerId], references: [id])
    customerId          String
}

model CustomerRatingsToAgents {
    id                    String  @id @default(cuid())
    propertyStarsCount    Int?    @default(0)
    propertyRatingMessage String?
    userStarsCount        Int     @default(0)
    userRatingMessage     String
    fileKey               String?
    filePublicUrl         String?

    cloudinaryPublicId    String?
    cloudinaryUrl         String?

    propertyId    String?
    ratedByUserId String
    ratedToUserId String
    connectionId  String?

    connection CustomerAgentConnections? @relation(fields: [connectionId], references: [id], onDelete: Cascade)
    property   Property?                 @relation(fields: [propertyId], references: [id], onDelete: Cascade)
    ratedTo    User                      @relation(fields: [ratedToUserId], references: [id], onDelete: Cascade)
    ratedBy    Customer                  @relation(fields: [ratedByUserId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model CustomerAgentConnectionMessages {
    id      String  @id @default(cuid())
    content String
    read    Boolean

    senderId String

    connectionId String
    connection   CustomerAgentConnections @relation(fields: [connectionId], references: [id], onDelete: Cascade)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

enum CustomerAgentConnectionChatStateEnum {
    CLOSED
    ACCEPTED
    PENDING
    REJECTED
}

model CustomerAgentConnections {
    id                          String                               @id @default(cuid())
    customerId                  String
    agentId                     String
    state                       CustomerAgentConnectionChatStateEnum @default(PENDING)
    customerUnseenMessagesCount Int                                  @default(0)
    agentUnseenMessagesCount    Int                                  @default(0)

    deletedAt DateTime?

    propertyId String?
    property   Property? @relation(fields: [propertyId], references: [id], onDelete: Cascade)

    rating CustomerRatingsToAgents[]

    customer Customer @relation(fields: [customerId], references: [id])
    agent    User     @relation(fields: [agentId], references: [id], onDelete: Cascade)

    messages CustomerAgentConnectionMessages[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model CustomerPropertyViewingHistory {
    id         String @id @default(cuid())
    customerId String
    propertyId String

    user     Customer @relation(fields: [customerId], references: [id])
    property Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model ReportedUserFromChat {
    id                  String    @id @default(cuid())
    reportingUserId     String?
    reportingUser       User?     @relation(name: "reportingUser", fields: [reportingUserId], references: [id], onDelete: Cascade)
    reportedUserId      String
    reportedUser        User      @relation(name: "reportedUser", fields: [reportedUserId], references: [id])
    reasonForReporting  String
    reportingCustomerId String?
    reportingCustomer   Customer? @relation(fields: [reportingCustomerId], references: [id])
    createdAt           DateTime  @default(now())
    updatedAt           DateTime  @updatedAt
}
