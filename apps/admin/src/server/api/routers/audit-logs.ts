import { z } from "zod";
import { adminProcedure, createTRPCRouter, protectedProcedure } from "../trpc";
import { TRPCError } from "@trpc/server";
import type { AuditActionEnum, AuditEntityEnum, AuditLog } from "@repo/database";

const auditLogsRouter = createTRPCRouter({
  getAuditLogs: protectedProcedure
    .input(
      z.object({
        page: z.number().optional().default(1),
        take: z.number().optional().default(20),
        entityType: z.enum(["USER", "PROPERTY", "POST", "PARTNER", "COMPANY", "ADMIN_USER"]).optional(),
        action: z.enum(["CREATE", "UPDATE", "DELETE", "STATUS_CHANGE", "APPROVE", "REJECT", "ACTIVATE", "DEACTIVATE", "RESTORE"]).optional(),
        performedById: z.string().optional(),
        entityId: z.string().optional(),
        dateFrom: z.date().optional(),
        dateTo: z.date().optional(),
        search: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const { page, take, entityType, action, performedById, entityId, dateFrom, dateTo, search } = input;

      // Build where condition
      const where: AuditLogWhereInput = {};

      if (entityType) {
        where.entityType = entityType;
      }

      if (action) {
        where.action = action;
      }

      if (performedById) {
        where.performedById = performedById;
      }

      if (entityId) {
        where.entityId = entityId;
      }

      if (dateFrom || dateTo) {
        where.createdAt = {};
        if (dateFrom) {
          where.createdAt.gte = dateFrom;
        }
        if (dateTo) {
          where.createdAt.lte = dateTo;
        }
      }

      if (search) {
        where.OR = [
          { entityTitle: { contains: search, mode: "insensitive" } },
          { performedByName: { contains: search, mode: "insensitive" } },
          { reason: { contains: search, mode: "insensitive" } },
        ];
      }

      try {
        // Get total count for pagination
        const totalLogs = await ctx.db.auditLog.count({ where });

        // Get audit logs
        const auditLogs = await ctx.db.auditLog.findMany({
          where,
          take,
          skip: (page - 1) * take,
          orderBy: {
            createdAt: "desc",
          },
          select: {
            id: true,
            action: true,
            entityType: true,
            entityId: true,
            entityTitle: true,
            performedById: true,
            performedByName: true,
            reason: true,
            createdAt: true,
            changes: true,
            metadata: true,
            performedBy: {
              select: {
                id: true,
                name: true,
                email: true,
                userType: true,
              },
            },
          },
        });

        return {
          auditLogs,
          totalPages: Math.ceil(totalLogs / take),
          totalResults: totalLogs,
          currentPage: page,
        };
      } catch (error) {
        console.error("Error fetching audit logs:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch audit logs",
        });
      }
    }),

  getAuditLogDetails: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      try {
        const auditLog = await ctx.db.auditLog.findUnique({
          where: { id: input.id },
          include: {
            performedBy: {
              select: {
                id: true,
                name: true,
                email: true,
                userType: true,
              },
            },
          },
        });

        if (!auditLog) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Audit log not found",
          });
        }

        return auditLog;
      } catch (error) {
        if (error instanceof TRPCError) throw error;
        
        console.error("Error fetching audit log details:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch audit log details",
        });
      }
    }),

  getAuditLogsByEntity: protectedProcedure
    .input(
      z.object({
        entityType: z.enum(["USER", "PROPERTY", "POST", "PARTNER", "COMPANY", "ADMIN_USER"]),
        entityId: z.string(),
        page: z.number().optional().default(1),
        take: z.number().optional().default(10),
      })
    )
    .query(async ({ ctx, input }) => {
      const { entityType, entityId, page, take } = input;

      try {
        const where = {
          entityType,
          entityId,
        };

        // Get total count for pagination
        const totalLogs = await ctx.db.auditLog.count({ where });

        // Get audit logs for specific entity
        const auditLogs = await ctx.db.auditLog.findMany({
          where,
          take,
          skip: (page - 1) * take,
          orderBy: {
            createdAt: "desc",
          },
          select: {
            id: true,
            action: true,
            performedByName: true,
            reason: true,
            createdAt: true,
            changes: true,
            metadata: true,
            performedBy: {
              select: {
                id: true,
                name: true,
                userType: true,
              },
            },
          },
        });

        return {
          auditLogs,
          totalPages: Math.ceil(totalLogs / take),
          totalResults: totalLogs,
          currentPage: page,
        };
      } catch (error) {
        console.error("Error fetching entity audit logs:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch entity audit logs",
        });
      }
    }),

  getAuditLogStats: adminProcedure.query(async ({ ctx }) => {
    try {
      // Get stats for the last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const [
        totalLogs,
        recentLogs,
        actionStats,
        entityStats,
        adminStats,
      ] = await Promise.all([
        // Total audit logs
        ctx.db.auditLog.count(),
        
        // Recent logs (last 30 days)
        ctx.db.auditLog.count({
          where: {
            createdAt: {
              gte: thirtyDaysAgo,
            },
          },
        }),
        
        // Action breakdown
        ctx.db.auditLog.groupBy({
          by: ["action"],
          _count: {
            action: true,
          },
          where: {
            createdAt: {
              gte: thirtyDaysAgo,
            },
          },
        }),
        
        // Entity type breakdown
        ctx.db.auditLog.groupBy({
          by: ["entityType"],
          _count: {
            entityType: true,
          },
          where: {
            createdAt: {
              gte: thirtyDaysAgo,
            },
          },
        }),
        
        // Admin activity breakdown
        ctx.db.auditLog.groupBy({
          by: ["performedById", "performedByName"],
          _count: {
            performedById: true,
          },
          where: {
            createdAt: {
              gte: thirtyDaysAgo,
            },
          },
          orderBy: {
            _count: {
              performedById: "desc",
            },
          },
          take: 10,
        }),
      ]);

      return {
        totalLogs,
        recentLogs,
        actionStats: actionStats.map(stat => ({
          action: stat.action,
          count: stat._count.action,
        })),
        entityStats: entityStats.map(stat => ({
          entityType: stat.entityType,
          count: stat._count.entityType,
        })),
        adminStats: adminStats.map(stat => ({
          adminId: stat.performedById,
          adminName: stat.performedByName,
          count: stat._count.performedById,
        })),
      };
    } catch (error) {
      console.error("Error fetching audit log stats:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch audit log statistics",
      });
    }
  }),
});

export default auditLogsRouter;
