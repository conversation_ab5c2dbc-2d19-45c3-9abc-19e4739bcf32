import { z } from "zod";
import { adminProcedure, createTRPCRouter, protectedProcedure, roleBasedProcedure } from "../trpc";
import { TRPCError } from "@trpc/server";
import AddUpdatePartnersSchema from "../validations/add-update-partners.validation";
import { NotificationEnum } from "@repo/database";
import type { Prisma } from "@repo/database";
import generateAlphanumericString from "../utils/generateInviteCode";
import { env } from "~/env";
import axios from "axios";
import whereAdminConditionCreatedByAdminId from "../utils/where-admin-condition-created-by-admin-id";

const partnerRouter = createTRPCRouter({
  getAllPartners: roleBasedProcedure.query(({ ctx }) => {
    // Build where condition based on user role
    const whereAdminCondition = whereAdminConditionCreatedByAdminId(ctx.session.user.role, ctx.session.user.id);

    return ctx.db.user.findMany({
      where: whereAdminCondition,
      select: {
        id: true,
        name: true,
        email: true,
        emailVerified: true,
        phoneNumber: true,
        // pancardNumber: true,
        // adharcardNumber: true,
        statusUpdatedAt: true,
        statusUpdatedBy: true,
        statusUpdateRemarks: true,
        reraNumber: true,
        gstNumber: true,
        createdAt: true,
        updatedAt: true,
        active: true,
        verifiedAgent: true,
        experience: true,
        propertiesSold: true,
        rating: true,
        filePublicUrl: true,
        posts: {
          select: {
            id: true,
            content: true,
            totalComments: true,
            totalLikes: true,
            createdAt: true,
            updatedAt: true,
            media: {
              select: {
                filePublicUrl: true,
                mediaType: true,
              },
            },
            user: {
              select: {
                id: true,
                filePublicUrl: true,
                name: true,
                company: {
                  select: {
                    companyName: true,
                  },
                },
              },
            },
            comments: {
              select: {
                comment: true,
                isPinned: true,
                createdAt: true,
                user: {
                  select: {
                    id: true,
                    name: true,
                    filePublicUrl: true,
                    companyDetails: {
                      select: {
                        companyName: true,
                      },
                    },
                  },
                },
              },
            },
            likes: {
              select: {
                id: true,
                postId: true,
              },
              take: 1,
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });
  }),

  getPartners: protectedProcedure
    .input(
      z.object({
        active: z.enum(["active", "inactive"]),
        take: z.number().optional(),
        page: z.number().optional().default(1),
      }),
    )
    .query(async ({ ctx, input }) => {
      // Build where condition based on user role
      const whereAdminCondition = whereAdminConditionCreatedByAdminId(ctx.session.user.role, ctx.session.user.id);
      const { take, page } = input;

      // Get total count for pagination
      const totalPartners = await ctx.db.user.count({
        where: {
          ...whereAdminCondition,
          active: input.active === "active" ? true : false,
        },
      });

      const partners = await ctx.db.user.findMany({
        ...(take && { take: take }),
        ...(page && take && { skip: (page - 1) * take }),
        where: {
          ...whereAdminCondition,
          active: input.active === "active" ? true : false,
        },
        select: {
          id: true,
          name: true,
          email: true,
          emailVerified: true,
          phoneNumber: true,
          statusUpdatedAt: true,
          statusUpdatedBy: true,
          statusUpdateRemarks: true,
          reraNumber: true,
          gstNumber: true,
          createdAt: true,
          updatedAt: true,
          active: true,
          verifiedAgent: true,
          experience: true,
          propertiesSold: true,
          rating: true,
          filePublicUrl: true,
          cloudinaryProfileImageUrl: true,
        },
      });

      return {
        partners,
        totalPages: take ? Math.ceil(totalPartners / take) : 1,
        totalResults: totalPartners,
      };
    }),

  getPartnerDetails: protectedProcedure
    .input(z.object({ agentId: z.string() }))
    .query(async ({ input, ctx }) => {
      // Build where condition based on user role
      const whereAdminCondition = whereAdminConditionCreatedByAdminId(ctx.session.user.role, ctx.session.user.id);
      return ctx.db.user.findFirst({
        where: {
          ...whereAdminCondition,
          id: input.agentId,
        },
        select: {
          id: true,
          name: true,
          userLocation: true,
          bio: true,
          propertiesSold: true,
          experience: true,
          createdAt: true,
          bgFileKey: true,
          bgFilePublicUrl: true,
          fileKey: true,
          filePublicUrl: true,
          cloudinaryBgImageUrl: true,
          cloudinaryProfileImageUrl: true,
          cloudinaryBgImagePublicId: true,
          cloudinaryProfileImagePublicId: true,
          rating: true,
          reviews: true,
          company: true,
          properties: {
            include: {
              areaUnit: true,
              utilities: true,
              amenities: true,
              user: {
                select: {
                  id: true,
                  company: true,
                  companyDetails: true,
                },
              },
              customerFavourites: true,
              mediaSections: {
                include: {
                  media: {
                    select: {
                      id: true,
                      fileKey: true,
                      filePublicUrl: true,
                      cloudinaryId: true,
                      cloudinaryUrl: true,
                    },
                  },
                },
              },
            },
            where: {
              propertyStatus: "ACTIVE",
            },
          },
          operationArea: true,
          posts: {
            include: {
              user: {
                select: {
                  id: true,
                  filePublicUrl: true,
                  cloudinaryProfileImageUrl: true,
                  name: true,
                  company: {
                    select: {
                      companyName: true,
                    },
                  },
                },
              },
              media: {
                select: {
                  filePublicUrl: true,
                  cloudinaryUrl: true,
                  cloudinaryId: true,
                  mediaType: true,
                },
              },
              comments: {
                select: {
                  comment: true,
                  isPinned: true,
                  createdAt: true,
                  user: {
                    select: {
                      id: true,
                      name: true,
                      filePublicUrl: true,
                      companyDetails: {
                        select: {
                          companyName: true,
                        },
                      },
                    },
                  },
                  customer: {
                    select: {
                      id: true,
                      name: true,
                      profileImagePublicUrl: true,
                    },
                  },
                  customerId: true,
                  userId: true,
                },
              },
              likes: {
                where: {
                  userId: input.agentId,
                },
                select: {
                  id: true,
                  postId: true,
                },
              },
            },
            orderBy: {
              createdAt: "desc",
            },
            where: {
              deletedAt: null,
            },
          },

          languages: true,
          customerRatingsToAgents: true,
          coustomerConnections: {
            where: {
              state: "ACCEPTED",
              deletedAt: null,
            },
          },
          receivedConnectionRequests: {
            where: {
              status: "ACCEPTED",
              deletedAt: null,
            },
          },
          sentConnectionRequests: {
            where: {
              status: "ACCEPTED",
              deletedAt: null,
            },
          },
        },
      });
    }),

  getPartnerById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(({ ctx, input }) => {
      // Build where condition based on user role
      const whereAdminCondition = whereAdminConditionCreatedByAdminId(ctx.session.user.role, ctx.session.user.id);
      
      return ctx.db.user.findFirst({
        where: {
          ...whereAdminCondition,
          id: input.id,
        },
        select: {
          id: true,
          name: true,
          email: true,
          phoneNumber: true,
          pancardNumber: true,
          adharcardNumber: true,
          reraNumber: true,
          gstNumber: true,
          cityId: true,
          referredByUserId: true,
        },
      });
    }),

  createPartner: protectedProcedure
    .input(AddUpdatePartnersSchema)
    .mutation(async ({ ctx, input }) => {
      const {
        name,
        phoneNumber,
        email,
        pancardNumber,
        adharcardNumber,
        reraNumber,
        gstNumber,
        cityId,
        referredBy,
      } = input;

      const whereCondition: Prisma.UserWhereInput = {
        OR: [
          { email: email },
          { phoneNumber: phoneNumber },
        ],
      };

      if (pancardNumber) {
        whereCondition.OR?.push({ pancardNumber: pancardNumber });
      }

      if (reraNumber) {
        whereCondition.OR?.push({ reraNumber: reraNumber });
      }
      if (gstNumber) {
        whereCondition.OR?.push({ gstNumber: gstNumber });
      }
      if (adharcardNumber) {
        whereCondition.OR?.push({ adharcardNumber: adharcardNumber });
      }

      // Check for user already exists or not
      try {
        const isUser = await ctx.db.user.findFirst({
          where: whereCondition,
          include: {
            operationArea: {
              select: {
                id: true,
                name: true,
                areaAddressComponents: true,
                areaGooglePlaceId: true,
                areaLat: true,
                areaLng: true,
              },
            },
          },
        });

        // If a user exists, check which specific field is causing the conflict
        if (isUser) {
          if (isUser.phoneNumber === phoneNumber) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message:
                "This phone number is already registered with another account. Please use a different phone number.",
            });
          }

          if (isUser.email === email) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message:
                "This email address is already in use. Please use a different email address.",
            });
          }

          if (pancardNumber && isUser.pancardNumber === pancardNumber) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message:
                "This PAN card number is already registered with another account. Please verify your information.",
            });
          }

          if (adharcardNumber && isUser.adharcardNumber === adharcardNumber) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message:
                "This Aadhar card number is already registered with another account. Please verify your information.",
            });
          }

          if (reraNumber && isUser.reraNumber === reraNumber) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message:
                "This RERA number is already registered with another account. Please verify your information.",
            });
          }

          if (gstNumber && isUser.gstNumber === gstNumber) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message:
                "This GST number is already registered with another account. Please verify your information.",
            });
          }

          // If we reach here, there's a duplicate but we don't know which field specifically
          // throw new TRPCError({
          //   code: "BAD_REQUEST",
          //   message:
          //     "A user with similar information already exists. Please verify your details.",
          // });
        }
      } catch (error) {
        // If it's already a TRPC error, rethrow it
        if (error instanceof TRPCError) {
          throw error;
        }

        // Otherwise, log the error and throw a generic error
        console.error("Error checking for existing user:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message:
            "An error occurred while checking for existing users. Please try again later.",
        });
      }

      let referredUserId = null;
      if (referredBy) {
        const referredUser = await ctx.db.user.findFirst({
          where: {
            inviteCode: referredBy,
          },
        });

        if (!referredUser) {
          throw new TRPCError({
            message: "Referral code doesn't exist.",
            code: "BAD_REQUEST",
          });
        }

        referredUserId = referredUser.id;

        await ctx.db.$transaction(async (prisma) => {
          //added the INR 100 to the user whose invite code is used
          await prisma.user.update({
            where: {
              id: referredUser.id,
            },
            data: {
              walletBalanceInCents: {
                increment: 10000,
              },
            },
          });

          //add the entry in walletTransaction Table
          await prisma.walletTransaction.create({
            data: {
              message: `Referral bonus of 100 points credited to your wallet from ${name}`,
              amountInCents: 10000,
              userId: referredUser.id,
            },
          });

          //add the notification for the amount credited in wallet
          await prisma.notification.create({
            data: {
              receiverId: referredUser.id,
              description: `${name} used your referral code for signing up! You have received 100 points in your wallet.`,
              title: "Referral Bonus Received",
              metaData: {},
              type: NotificationEnum.WALLET,
            },
          });
        });
      }

      const createdUser = await ctx.db.user.create({
        data: {
          name: name,
          phoneNumber: phoneNumber,
          email: email,
          pancardNumber: pancardNumber,
          adharcardNumber: adharcardNumber,
          reraNumber: reraNumber,
          gstNumber: gstNumber,
          cityId: cityId,
          inviteCode: generateAlphanumericString(),
          referredBy: referredBy,
          referredByUserId: referredUserId,
          createdByAdminId: ctx.session.user.id,
        },
      });

      return {
        success: true,
        statusCode: 200,
        messageTitle: "Success !",
        messageDescription: "Registration successfull.",
        data: createdUser,
      };
    }),

  updatePartner: protectedProcedure
    .input(AddUpdatePartnersSchema.extend({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const {
        id,
        name,
        phoneNumber,
        email,
        // pancardNumber,
        adharcardNumber,
        reraNumber,
        gstNumber,
        cityId,
      } = input;

      if (!phoneNumber) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "Unauthorized",
        });
      }

      const whereCondition: Prisma.UserWhereInput = {
        OR: [{ email: email }],
        NOT: { phoneNumber: phoneNumber },
        id: {
          not: id,
        },
      };

      if (reraNumber) {
        whereCondition.OR?.push({ reraNumber: reraNumber });
      }
      if (gstNumber) {
        whereCondition.OR?.push({ gstNumber: gstNumber });
      }
      if (adharcardNumber) {
        whereCondition.OR?.push({ adharcardNumber: adharcardNumber });
      }

      // check for user already exists or not
      const isUser = await ctx.db.user.findFirst({
        where: whereCondition,
      });

      if (isUser?.email === email) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Email already exists !",
        });
      }

      if (reraNumber && isUser?.reraNumber === reraNumber) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "RERA Number already exists !",
        });
      }
      if (adharcardNumber && isUser?.adharcardNumber === adharcardNumber) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Aadhar card number already exists !",
        });
      }
      if (gstNumber && isUser?.gstNumber === gstNumber) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "GST Number already exists !",
        });
      }
      // Build where condition based on user role
      const whereAdminCondition = whereAdminConditionCreatedByAdminId(ctx.session.user.role, ctx.session.user.id);

      const partnerAllowedToUpdate = await ctx.db.user.findFirst({
        select: {
          id: true,
        },
        where: {
          ...whereAdminCondition,
          id: id,
        },
      });

      if (!partnerAllowedToUpdate) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "Unauthorized",
        });
      }

      await ctx.db.user.update({
        data: {
          name: name,
          email: email,
          reraNumber: reraNumber,
          gstNumber: gstNumber,
          //   pancardNumber: pancardNumber,
          adharcardNumber: adharcardNumber,
          cityId: cityId,
          phoneNumber: phoneNumber,
        },
        where: {
          id: id,
        },
      });
      const partner = await ctx.db.user.findUnique({
        where: { id, active: true },
        select: {
          id: true,
          name: true,
          filePublicUrl: true,
          userLocation: true,
        },
      });
      try {
        await axios.put(
          `${env.MEILI_SEARCH_URL}/indexes/${env.PARTNER_INDEX}/documents`,
          partner,
          {
            headers: {
              "Content-Type": "application/json",
              "X-MEILI-API-KEY": env.MEILI_SEARCH_KEY,
              Authorization: `Bearer ${env.MEILI_SEARCH_KEY}`,
            },
          },
        );
      } catch (error) {
        console.error("Error updating partner in search index:", error);
      }

      return {
        success: true,
        statusCode: 200,
        messageTitle: "Success !",
        messageDescription: "User updated successfully.",
      };
    }),

  deletePartner: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { id } = input;

      try {
        const user = await ctx.db.user.delete({
          where: {
            id: id,
          },
        });

        await axios.delete(
          `${env.MEILI_SEARCH_URL}/indexes/${env.PARTNER_INDEX}/documents/${user.id}`,
          {
            headers: {
              "Content-Type": "application/json",
              "X-MEILI-API-KEY": env.MEILI_SEARCH_KEY,
              Authorization: `Bearer ${env.MEILI_SEARCH_KEY}`,
            },
          },
        );

        return {
          message: `user ${user.name} deleted successfully.`,
        };
      } catch (err) {
        console.error("Error deleting partner:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete partner",
        });
      }
    }),

  getCities: protectedProcedure.query(async ({ ctx }) => {
    try {
      const cities = await ctx.db.city.findMany();

      return cities;
    } catch (err) {
      console.log(err);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch cities",
      });
    }
  }),

  approvePartner: protectedProcedure
    .input(z.object({ partnerId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const userName = ctx.session.user.name;
      const { partnerId } = input;
      const partner = await ctx.db.user.findFirst({
        where: { id: partnerId },
        select: {
          id: true,
          name: true,
          filePublicUrl: true,
          userLocation: true,
        },
      });

      if (!partner) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: `Partner with id: ${partnerId} not found !`,
        });
      }

      const query = await ctx.db.user.update({
        where: { id: partnerId },
        data: {
          active: true,
          statusUpdateRemarks: "User approved by admin",
          statusUpdatedAt: new Date(),
          statusUpdatedBy: userName,
        },
      });

      await axios.post(
        `${env.MEILI_SEARCH_URL}/indexes/${env.PARTNER_INDEX}/documents`,
        partner,
        {
          headers: {
            "Content-Type": "application/json",
            "X-MEILI-API-KEY": env.MEILI_SEARCH_KEY,
            Authorization: `Bearer ${env.MEILI_SEARCH_KEY}`,
          },
        },
      );

      return {
        message: `Partner with id: ${query.id} has been approved`,
      };
    }),

  rejectPartner: protectedProcedure
    .input(z.object({ partnerId: z.string(), remarks: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { partnerId, remarks } = input;
      const userName = ctx.session.user.name;
      const partner = await ctx.db.user.findFirst({
        where: { id: partnerId },
      });

      if (!partner) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: `Partner with id: ${partnerId} not found !`,
        });
      }

      const query = await ctx.db.user.update({
        where: { id: partnerId },
        data: {
          active: false,
          statusUpdateRemarks: remarks,
          statusUpdatedAt: new Date(),
          statusUpdatedBy: userName,
        },
      });
      await axios.delete(
        `${env.MEILI_SEARCH_URL}/indexes/${env.PARTNER_INDEX}/documents/${partnerId}`,
        {
          headers: {
            "Content-Type": "application/json",
            "X-MEILI-API-KEY": env.MEILI_SEARCH_KEY,
            Authorization: `Bearer ${env.MEILI_SEARCH_KEY}`,
          },
        },
      );
      return {
        message: `Partner with id: ${query.id} has been rejected`,
      };
    }),
});

export default partnerRouter;
