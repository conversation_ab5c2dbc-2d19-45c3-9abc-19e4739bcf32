
import type { AdminUserTypeEnum, Prisma } from "@repo/database";

const whereAdminConditionCreatedByAdminId = (role: AdminUserTypeEnum, userId: string): Prisma.UserWhereInput => {
  const whereAdminCondition: Prisma.UserWhereInput = {};

  // If user is MANAGER, only show partners they created
  if (role === "MANAGER") {
    whereAdminCondition.createdByAdminId = userId;
  }
  // If user is ADMIN, show all partners (no additional filtering)
  return whereAdminCondition;
};

export default whereAdminConditionCreatedByAdminId;
